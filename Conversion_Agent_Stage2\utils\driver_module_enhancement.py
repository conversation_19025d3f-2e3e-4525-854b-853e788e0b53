"""
Driver Module Enhancement Utility for Stage 2 Conversion Analysis.

This module implements the driver module approach where multiple responsible modules
are combined into a single module for enhancement, then decomposed back with only
functionally changed modules saved as attempt files.
"""

import re
import os
import glob
from typing import Dict, List, Tuple, Any
from Conversion_Agent_Stage2.prompts.driver_module_enhancement_prompt import create_driver_module_enhancement_prompt
from Conversion_Agent_Stage2.state.state import DriverModuleEnhancementOutput


class DriverModuleEnhancer:
    """
    Handles the driver module enhancement approach for multiple responsible modules.
    """
    
    def __init__(self, llm_client):
        """
        Initialize the driver module enhancer.

        Args:
            llm_client: The LLM client for AI enhancement
        """
        self.llm_client = llm_client

    def get_next_global_attempt_number(self, module_name: str, base_metadata_path: str) -> int:
        """
        Get the next global attempt number for a module across all statements.

        Args:
            module_name: Name of the module (e.g., 'join', 'nvl')
            base_metadata_path: Base path to the metadata directory

        Returns:
            Next global attempt number to use
        """
        print(f"🔍 Finding next global attempt number for {module_name}...")

        max_attempt = 0
        statements_found = []

        # Search pattern for qm_feature_modules directories
        feature_modules_pattern = os.path.join(base_metadata_path, "qm_feature_modules", "*")
        statement_dirs = glob.glob(feature_modules_pattern)

        print(f"🔍 Scanning {len(statement_dirs)} statement directories for {module_name} attempts...")

        for statement_dir in statement_dirs:
            if os.path.isdir(statement_dir):
                statement_num = os.path.basename(statement_dir)

                # Look for attempt files for this module in this statement
                attempt_pattern = os.path.join(statement_dir, f"{module_name}_attempt_*.py")
                attempt_files = glob.glob(attempt_pattern)

                if attempt_files:
                    print(f"📁 Statement {statement_num}: Found {len(attempt_files)} {module_name} attempts")

                for attempt_file in attempt_files:
                    # Extract attempt number from filename
                    match = re.search(rf'{module_name}_attempt_(\d+)\.py', attempt_file)
                    if match:
                        attempt_num = int(match.group(1))
                        if attempt_num > max_attempt:
                            max_attempt = attempt_num
                        statements_found.append(f"Statement {statement_num}: attempt_{attempt_num}")

        next_attempt = max_attempt + 1

        if statements_found:
            print(f"📊 Found existing {module_name} attempts:")
            for statement_info in statements_found:
                print(f"   - {statement_info}")
            print(f"🎯 Next global attempt number: {next_attempt} (max found: {max_attempt})")
        else:
            print(f"📊 No previous {module_name} attempts found")
            print(f"🎯 Starting with global attempt number: {next_attempt}")

        return next_attempt
    
    def combine_responsible_modules(self, responsible_modules: List[Tuple[str, str, str]]) -> Tuple[str, Dict[str, Dict[str, str]]]:
        """
        Combine all responsible modules into a single driver module.
        
        Args:
            responsible_modules: List of (feature_name, module_path, module_code) tuples
            
        Returns:
            Tuple of (combined_code, module_boundaries)
        """
        combined_code = ""
        module_boundaries = {}
        
        print(f"📦 Combining {len(responsible_modules)} modules into driver module")
        
        for feature_name, module_path, module_code in responsible_modules:
            # Create unique boundary markers
            start_marker = f"# === MODULE: {feature_name} START ==="
            end_marker = f"# === MODULE: {feature_name} END ==="
            
            # Add module to combined code with boundaries
            combined_code += f"\n{start_marker}\n"
            combined_code += module_code
            combined_code += f"\n{end_marker}\n"
            
            # Track boundaries for later decomposition
            module_boundaries[feature_name] = {
                'start_marker': start_marker,
                'end_marker': end_marker,
                'module_path': module_path
            }
            
            print(f"   📋 Added module: {feature_name}")
        
        return combined_code, module_boundaries
    
    def enhance_combined_module(self, combined_code: str, ai_context: Dict[str, Any]) -> str:
        """
        Send combined module to AI for enhancement using structured output.

        Args:
            combined_code: The combined module code
            ai_context: Context for AI enhancement

        Returns:
            Enhanced combined module code
        """
        print("🤖 Starting AI enhancement of combined module")

        # Create enhancement prompt for driver module
        # Extract the correct parameters from ai_context
        conversion_context = ai_context.get('conversion_context', {})

        # Debug: Check the type and content of conversion_context
        print(f"🔍 Debug: conversion_context type: {type(conversion_context)}")
        print(f"🔍 Debug: ai_context keys: {list(ai_context.keys())}")

        # Safely extract parameters with proper type checking
        if isinstance(conversion_context, dict):
            oracle_statement = conversion_context.get('original_source_statement', '')
            expected_postgres = conversion_context.get('ai_converted_statement', '')
            applied_modules_output = conversion_context.get('original_target_statement', '')
        else:
            # Fallback to direct ai_context parameters
            oracle_statement = ai_context.get('oracle_statement', '')
            expected_postgres = ai_context.get('ai_corrected_output', '')
            applied_modules_output = ai_context.get('current_target_output', '')

        prompt = create_driver_module_enhancement_prompt(
            combined_module_code=combined_code,
            oracle_statement=oracle_statement,
            expected_postgres=expected_postgres,
            applied_modules_output=applied_modules_output,
            ai_feedback=ai_context.get('ai_comparison_feedback'),
            previous_attempts=ai_context.get('attempt_history')
        )

        # Use structured output for reliable AI enhancement
        structured_llm = self.llm_client.with_structured_output(DriverModuleEnhancementOutput)
        ai_result = structured_llm.invoke(prompt)

        print("✅ AI enhancement completed successfully")
        print(f"📊 Analysis: {ai_result.analysis}")

        # Log changes made
        if ai_result.changes_made:
            print(f"🔧 Changes made to {len(ai_result.changes_made)} modules:")
            for change in ai_result.changes_made:
                module_name = change.get('module_name', 'Unknown')
                description = change.get('changes_description', 'No description')
                print(f"   - {module_name}: {description}")

        return ai_result.enhanced_combined_code
    
    def extract_module_from_combined(self, enhanced_combined_code: str, module_info: Dict[str, str]) -> str:
        """
        Extract a specific module from the enhanced combined code.
        
        Args:
            enhanced_combined_code: The enhanced combined module code
            module_info: Module boundary information
            
        Returns:
            Extracted module code
        """
        start_marker = module_info['start_marker']
        end_marker = module_info['end_marker']
        
        # Find the module content between markers
        start_pattern = re.escape(start_marker)
        end_pattern = re.escape(end_marker)
        
        pattern = f"{start_pattern}(.*?){end_pattern}"
        match = re.search(pattern, enhanced_combined_code, re.DOTALL)
        
        if match:
            module_content = match.group(1).strip()
            return module_content
        else:
            print(f"⚠️ Could not extract module with markers: {start_marker}")
            return ""
    
    def has_functional_changes(self, original_code: str, enhanced_code: str) -> bool:
        """
        Detect if there are actual functional changes (ignore comments/formatting).
        
        Args:
            original_code: Original module code
            enhanced_code: Enhanced module code
            
        Returns:
            True if functional changes detected, False otherwise
        """
        def normalize_functional_code(code: str) -> str:
            """Remove comments and normalize whitespace for comparison."""
            lines = []
            for line in code.split('\n'):
                # Remove full-line comments
                if line.strip().startswith('#'):
                    continue
                # Remove inline comments
                line = re.sub(r'#.*$', '', line)
                # Normalize whitespace
                line = ' '.join(line.split())
                if line:
                    lines.append(line)
            return '\n'.join(lines)
        
        original_functional = normalize_functional_code(original_code)
        enhanced_functional = normalize_functional_code(enhanced_code)
        
        return original_functional != enhanced_functional
    
    def decompose_and_detect_changes(self, original_modules: Dict[str, str], enhanced_combined_code: str, 
                                   module_boundaries: Dict[str, Dict[str, str]]) -> Dict[str, Dict[str, Any]]:
        """
        Decompose enhanced combined code and detect functional changes.
        
        Args:
            original_modules: Dict of {feature_name: original_code}
            enhanced_combined_code: Enhanced combined module code
            module_boundaries: Module boundary information
            
        Returns:
            Dict of decomposition results for each module
        """
        print("🔍 Decomposing enhanced combined module and detecting changes")
        
        results = {}
        
        for feature_name, original_code in original_modules.items():
            if feature_name in module_boundaries:
                # Extract enhanced module from combined code
                enhanced_module_code = self.extract_module_from_combined(
                    enhanced_combined_code, 
                    module_boundaries[feature_name]
                )
                
                if enhanced_module_code:
                    # Check for functional changes
                    has_changes = self.has_functional_changes(original_code, enhanced_module_code)
                    
                    results[feature_name] = {
                        'status': 'changed' if has_changes else 'unchanged',
                        'enhanced_code': enhanced_module_code if has_changes else None,
                        'action': 'create_attempt' if has_changes else 'use_original',
                        'module_path': module_boundaries[feature_name]['module_path']
                    }
                    
                    status_icon = "✅" if has_changes else "➖"
                    status_text = "Functional changes detected" if has_changes else "No functional changes"
                    print(f"   {status_icon} {feature_name}: {status_text}")
                else:
                    print(f"   ⚠️ {feature_name}: Could not extract from combined module")
                    results[feature_name] = {
                        'status': 'error',
                        'enhanced_code': None,
                        'action': 'use_original',
                        'module_path': module_boundaries[feature_name]['module_path']
                    }
            else:
                print(f"   ⚠️ {feature_name}: No boundary information found")
                results[feature_name] = {
                    'status': 'error',
                    'enhanced_code': None,
                    'action': 'use_original',
                    'module_path': ''
                }
        
        return results

    def save_decomposed_modules(self, decomposition_results: Dict[str, Dict[str, Any]],
                              feature_modules_dir: str, base_metadata_path: str) -> Dict[str, List[str]]:
        """
        Save only modules that have functional changes as attempt files using global attempt numbering.

        Args:
            decomposition_results: Results from decomposition and change detection
            feature_modules_dir: Directory to save attempt modules
            base_metadata_path: Base path to metadata for global attempt number discovery

        Returns:
            Dict with lists of saved and unchanged modules
        """
        print("💾 Saving decomposed modules with global attempt numbering")

        saved_modules = []
        unchanged_modules = []
        global_attempt_numbers = {}  # Track global attempt numbers used per module

        # Ensure feature modules directory exists
        os.makedirs(feature_modules_dir, exist_ok=True)

        for feature_name, result in decomposition_results.items():
            if result['action'] == 'create_attempt' and result['enhanced_code']:
                # Get next global attempt number for this module
                global_attempt_number = self.get_next_global_attempt_number(feature_name, base_metadata_path)
                global_attempt_numbers[feature_name] = global_attempt_number  # Track for return

                # Save as attempt module with global attempt number
                attempt_filename = f"{feature_name}_attempt_{global_attempt_number}.py"
                attempt_filepath = os.path.join(feature_modules_dir, attempt_filename)

                try:
                    with open(attempt_filepath, 'w', encoding='utf-8') as f:
                        f.write(result['enhanced_code'])

                    saved_modules.append(feature_name)
                    print(f"   ✅ {feature_name}: Saved as {attempt_filename} (global attempt #{global_attempt_number})")

                except Exception as e:
                    print(f"   ❌ {feature_name}: Failed to save - {str(e)}")
                    unchanged_modules.append(feature_name)

            elif result['action'] == 'use_original':
                # Keep using original module
                unchanged_modules.append(feature_name)
                print(f"   ➖ {feature_name}: Using original module")
            else:
                # Error case - use original
                unchanged_modules.append(feature_name)
                print(f"   ⚠️ {feature_name}: Error in processing - using original module")

        # Log global attempt numbers summary
        if global_attempt_numbers:
            print(f"🎯 Global attempt numbers assigned:")
            for module_name, attempt_num in global_attempt_numbers.items():
                print(f"   - {module_name}: global attempt #{attempt_num}")

        return {
            'saved_modules': saved_modules,
            'unchanged_modules': unchanged_modules,
            'global_attempt_numbers': global_attempt_numbers  # Return actual global attempt numbers used
        }

    def driver_module_enhancement_workflow(self, responsible_modules_with_code: List[Tuple[str, str, str]],
                                         ai_context: Dict[str, Any], feature_modules_dir: str,
                                         base_metadata_path: str) -> Dict[str, Any]:
        """
        Complete driver module enhancement workflow with global attempt numbering.

        Args:
            responsible_modules_with_code: List of (feature_name, module_path, module_code) tuples
            ai_context: Context for AI enhancement
            feature_modules_dir: Directory to save attempt modules
            base_metadata_path: Base path to metadata for global attempt number discovery

        Returns:
            Dict with enhancement results and module information
        """
        print(f"🔧 Starting driver module enhancement for {len(responsible_modules_with_code)} modules")

        # Phase 1: Combine modules
        combined_code, module_boundaries = self.combine_responsible_modules(responsible_modules_with_code)

        # Phase 2: Enhance combined module
        enhanced_combined_code = self.enhance_combined_module(combined_code, ai_context)

        # Phase 3: Decompose and detect changes
        original_modules = {name: code for name, _, code in responsible_modules_with_code}
        decomposition_results = self.decompose_and_detect_changes(
            original_modules, enhanced_combined_code, module_boundaries
        )

        # Phase 4: Save only changed modules with global attempt numbering
        save_results = self.save_decomposed_modules(
            decomposition_results, feature_modules_dir, base_metadata_path
        )

        print(f"💾 Driver module enhancement complete:")
        print(f"   ✅ Modules with changes: {save_results['saved_modules']}")
        print(f"   ➖ Modules unchanged: {save_results['unchanged_modules']}")

        # Prepare results for return
        enhanced_modules = []
        for feature_name, result in decomposition_results.items():
            if result['action'] == 'create_attempt':
                enhanced_modules.append({
                    'feature_name': feature_name,
                    'module_path': result['module_path'],
                    'enhanced_code': result['enhanced_code'],
                    'code_changed': True
                })

        return {
            'success': True,
            'enhanced_modules': enhanced_modules,
            'saved_modules': save_results['saved_modules'],
            'unchanged_modules': save_results['unchanged_modules'],
            'total_modules': len(responsible_modules_with_code),
            'changed_modules_count': len(save_results['saved_modules']),
            'unchanged_modules_count': len(save_results['unchanged_modules']),
            'combined_module_code': combined_code,
            'enhanced_combined_code': enhanced_combined_code,
            'global_attempt_numbers': save_results.get('global_attempt_numbers', {})  # Include global attempt numbers
        }
